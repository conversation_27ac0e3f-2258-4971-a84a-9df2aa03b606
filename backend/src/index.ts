// Load environment variables first
import dotenv from 'dotenv';
import path from 'path';
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { ConversationOrchestrator } from './services/conversation-orchestrator.js';
import { WebSocketClient } from './types/index.js';
import { defaultConversationConfig } from './config/conversation.js';

const app = express();
const server = createServer(app);
const wss = new WebSocketServer({ server, path: '/ws' });

const PORT = process.env['PORT'] || 3000;

// Debug: Log environment variables
console.log('🔧 Environment check:');
console.log(`   NODE_ENV: ${process.env['NODE_ENV']}`);
console.log(`   PORT: ${PORT}`);
console.log(`   OPENAI_API_KEY: ${process.env['OPENAI_API_KEY'] ? '✅ Set' : '❌ Missing'}`);
console.log(`   OPENAI_MODEL: ${process.env['OPENAI_MODEL']}`);

// Store active conversations and clients
const activeConversations = new Map<string, ConversationOrchestrator>();
const connectedClients = new Map<string, WebSocketClient>();

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env['FRONTEND_URL'] || 'http://localhost:5173',
  credentials: true,
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API routes
app.get('/api', (_req, res) => {
  res.json({
    message: 'MiCA Therapy Simulation API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      conversations: '/api/conversations',
      websocket: '/ws',
    },
  });
});

// WebSocket connection handling
wss.on('connection', (ws, _req) => {
  const clientId = uuidv4();
  console.log(`🔌 New WebSocket connection established: ${clientId}`);

  // Create client object
  const client: WebSocketClient = {
    id: clientId,
    socket: ws,
    lastActivity: new Date().toISOString()
  };

  connectedClients.set(clientId, client);
  console.log(`👥 Total connected clients: ${connectedClients.size}`);

  ws.on('message', async (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log(`📨 Received message from client ${clientId}:`, data);

      // Update client activity
      client.lastActivity = new Date().toISOString();

      await handleWebSocketMessage(client, data);

    } catch (error) {
      console.error(`❌ Error processing message from client ${clientId}:`, error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format',
        timestamp: new Date().toISOString(),
      }));
    }
  });

  ws.on('close', () => {
    console.log(`🔌 WebSocket connection closed: ${clientId}`);

    // Remove client from active conversation if any
    if (client.conversationId) {
      const conversation = activeConversations.get(client.conversationId);
      if (conversation) {
        conversation.removeClient(clientId);
      }
    }

    // Remove client from connected clients
    connectedClients.delete(clientId);
    console.log(`👥 Total connected clients: ${connectedClients.size}`);
  });

  ws.on('error', (error) => {
    console.error(`❌ WebSocket error for client ${clientId}:`, error);
  });

  // Send welcome message
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Connected to MiCA Therapy Simulation',
    clientId: clientId,
    timestamp: new Date().toISOString(),
  }));
});

/**
 * Handle WebSocket messages
 */
async function handleWebSocketMessage(client: WebSocketClient, data: any): Promise<void> {
  console.log(`🔄 Handling message type: ${data.type} from client ${client.id}`);

  try {
    switch (data.type) {
      case 'start_conversation':
        await handleStartConversation(client, data);
        break;

      case 'pause_conversation':
        await handlePauseConversation(client, data);
        break;

      case 'resume_conversation':
        await handleResumeConversation(client, data);
        break;

      case 'clear_conversation':
        await handleClearConversation(client, data);
        break;

      case 'get_conversation_status':
        await handleGetConversationStatus(client, data);
        break;

      default:
        console.warn(`⚠️ Unknown message type: ${data.type}`);
        client.socket.send(JSON.stringify({
          type: 'error',
          message: `Unknown message type: ${data.type}`,
          timestamp: new Date().toISOString(),
        }));
    }
  } catch (error) {
    console.error(`❌ Error handling message type ${data.type}:`, error);
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Internal server error while processing message',
      timestamp: new Date().toISOString(),
    }));
  }
}

/**
 * Handle start conversation request
 */
async function handleStartConversation(client: WebSocketClient, data: any): Promise<void> {
  console.log(`🚀 Starting conversation for client ${client.id}`);

  try {
    // Create new conversation orchestrator
    const conversationId = uuidv4();

    // Handle partial config from frontend - merge with defaults
    let config = undefined;
    if (data.config) {
      // Create a merged config
      config = {
        ...defaultConversationConfig,
        conversation: {
          ...defaultConversationConfig.conversation,
          // Override maxTurns if provided
          ...(data.config.maxTurns && { maxTurns: data.config.maxTurns })
        }
      };

      console.log(`🔧 Using custom config with maxTurns: ${config.conversation.maxTurns}`);
    }

    const orchestrator = new ConversationOrchestrator(conversationId, config);

    // Store conversation and associate client
    activeConversations.set(conversationId, orchestrator);
    client.conversationId = conversationId;
    orchestrator.addClient(client);

    console.log(`✅ Conversation ${conversationId} created and started`);
    console.log(`📊 Active conversations: ${activeConversations.size}`);

    // Send confirmation to client
    client.socket.send(JSON.stringify({
      type: 'conversation_created',
      data: {
        conversationId: conversationId,
        status: 'starting'
      },
      timestamp: new Date().toISOString(),
    }));

    // Start the conversation (this will trigger the initial therapist greeting)
    await orchestrator.startConversation();

  } catch (error) {
    console.error(`❌ Error starting conversation:`, error);
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Failed to start conversation',
      timestamp: new Date().toISOString(),
    }));
  }
}

/**
 * Handle pause conversation request
 */
async function handlePauseConversation(client: WebSocketClient, _data: any): Promise<void> {
  console.log(`⏸️ Pausing conversation for client ${client.id}`);

  if (!client.conversationId) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'No active conversation to pause',
      timestamp: new Date().toISOString(),
    }));
    return;
  }

  const orchestrator = activeConversations.get(client.conversationId);
  if (orchestrator) {
    orchestrator.pauseConversation();
  }
}

/**
 * Handle resume conversation request
 */
async function handleResumeConversation(client: WebSocketClient, _data: any): Promise<void> {
  console.log(`▶️ Resuming conversation for client ${client.id}`);

  if (!client.conversationId) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'No conversation to resume',
      timestamp: new Date().toISOString(),
    }));
    return;
  }

  const orchestrator = activeConversations.get(client.conversationId);
  if (orchestrator) {
    orchestrator.resumeConversation();
  }
}

/**
 * Handle clear conversation request
 */
async function handleClearConversation(client: WebSocketClient, _data: any): Promise<void> {
  console.log(`🗑️ Clearing conversation for client ${client.id}`);

  if (!client.conversationId) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'No conversation to clear',
      timestamp: new Date().toISOString(),
    }));
    return;
  }

  const orchestrator = activeConversations.get(client.conversationId);
  if (orchestrator) {
    orchestrator.clearHistory();
  }
}

/**
 * Handle get conversation status request
 */
async function handleGetConversationStatus(client: WebSocketClient, _data: any): Promise<void> {
  console.log(`📊 Getting conversation status for client ${client.id}`);

  if (!client.conversationId) {
    client.socket.send(JSON.stringify({
      type: 'conversation_status',
      data: { status: 'no_active_conversation' },
      timestamp: new Date().toISOString(),
    }));
    return;
  }

  const orchestrator = activeConversations.get(client.conversationId);
  if (orchestrator) {
    const context = orchestrator.getContext();
    const messages = orchestrator.getMessages();

    client.socket.send(JSON.stringify({
      type: 'conversation_status',
      data: {
        conversationId: client.conversationId,
        status: context.status,
        currentTurn: context.currentTurn,
        maxTurns: context.maxTurns,
        messageCount: messages.length,
        isActive: orchestrator.isConversationActive()
      },
      timestamp: new Date().toISOString(),
    }));
  }
}

// Error handling middleware
app.use((err: any, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env['NODE_ENV'] === 'development' ? err.message : 'Internal server error',
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 MiCA Backend Server running on port ${PORT}`);
  console.log(`📡 WebSocket server available at ws://localhost:${PORT}/ws`);
  console.log(`🏥 Health check available at http://localhost:${PORT}/health`);
});
