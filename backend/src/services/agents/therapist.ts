// Therapist Agent Service for MiCA Therapy Simulation
import { OpenAIService } from '../openai.js';
import { AgentResponse, ConversationContext } from '../../types/index.js';
import { TherapistConfig, defaultConversationConfig } from '../../config/conversation.js';

export class TherapistAgentService {
  private openaiService: OpenAIService;
  private config: TherapistConfig;
  private conversationHistory: Array<{ role: string; content: string; timestamp: string }> = [];

  constructor(openaiService: OpenAIService, config?: TherapistConfig) {
    this.openaiService = openaiService;
    this.config = config || defaultConversationConfig.therapist;
    
    console.log(`👩‍⚕️ Therapist Agent initialized: ${this.config.persona.name}`);
    console.log(`🎯 Specialties: ${this.config.persona.specialties.join(', ')}`);
  }

  /**
   * Generate initial greeting message
   */
  async generateInitialGreeting(): Promise<AgentResponse> {
    console.log('👋 Generating therapist initial greeting...');
    
    const thinking = await this.generateThinking(
      'Starting the first session with a new patient. Need to create a welcoming, safe environment while gathering initial information.',
      'What should I say to make the patient feel comfortable and begin building rapport?'
    );

    return {
      message: this.config.prompts.initialGreeting,
      thinking,
      metadata: {
        sentiment: 'positive',
        motivationLevel: 'high',
        engagementLevel: 'high',
        confidence: 0.9,
        processingTime: 0
      }
    };
  }

  /**
   * Generate response to patient message
   */
  async generateResponse(
    patientMessage: string,
    conversationContext: ConversationContext
  ): Promise<AgentResponse> {
    console.log('👩‍⚕️ Therapist generating response to patient message...');
    console.log(`📝 Patient said: "${patientMessage.substring(0, 100)}..."`);

    const startTime = Date.now();

    try {
      // Build conversation context for the AI
      const systemPrompt = this.buildSystemPrompt();
      const conversationMessages = this.buildConversationMessages(patientMessage, conversationContext);

      // Generate the main response
      const response = await this.openaiService.generateResponse({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          ...conversationMessages
        ],
        temperature: 0.7,
        max_tokens: 300
      });

      // Generate thinking process
      const thinking = await this.generateThinking(
        `Patient just said: "${patientMessage}"`,
        'What therapeutic approach should I take? What is the patient really communicating?'
      );

      // Analyze the patient's message
      const [sentiment, engagement, motivation] = await Promise.all([
        this.openaiService.analyzeSentiment(patientMessage),
        this.openaiService.analyzeEngagement(patientMessage),
        this.openaiService.analyzeMotivation(patientMessage)
      ]);

      const processingTime = Date.now() - startTime;

      // Update conversation history
      this.conversationHistory.push({
        role: 'patient',
        content: patientMessage,
        timestamp: new Date().toISOString()
      });
      this.conversationHistory.push({
        role: 'therapist',
        content: response.message,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Therapist response generated in ${processingTime}ms`);
      console.log(`📊 Analysis - Sentiment: ${sentiment}, Engagement: ${engagement}, Motivation: ${motivation}`);

      return {
        message: response.message,
        thinking,
        metadata: {
          sentiment,
          motivationLevel: motivation,
          engagementLevel: engagement,
          confidence: response.metadata.confidence,
          processingTime
        }
      };

    } catch (error) {
      console.error('❌ Error generating therapist response:', error);
      
      return {
        message: "I hear you, and I want to make sure I understand what you're sharing with me. Could you tell me a bit more about that?",
        thinking: "I'm having some difficulty processing right now, but I want to stay present and supportive for the patient.",
        metadata: {
          sentiment: 'neutral',
          motivationLevel: 'medium',
          engagementLevel: 'medium',
          confidence: 0.5,
          processingTime: Date.now() - startTime
        }
      };
    }
  }

  /**
   * Generate thinking process for the therapist
   */
  private async generateThinking(context: string, question: string): Promise<string> {
    const thinkingPrompt = `
As ${this.config.persona.name}, a ${this.config.persona.background}

Context: ${context}

Consider:
- What therapeutic techniques might be most helpful here?
- What is the patient's emotional state and needs?
- How can I build rapport and trust?
- What underlying issues might be present?
- How should I pace this conversation?

Question: ${question}

Provide your internal therapeutic reasoning and approach:`;

    try {
      return await this.openaiService.generateThinking('therapist', context, thinkingPrompt);
    } catch (error) {
      console.error('❌ Error generating therapist thinking:', error);
      return 'Analyzing the patient\'s words and considering the most therapeutic response approach...';
    }
  }

  /**
   * Build system prompt for the therapist
   */
  private buildSystemPrompt(): string {
    return `${this.config.prompts.systemPrompt}

Current session context:
- Patient concerns: ${defaultConversationConfig.patient.concerns.join(', ')}
- Patient emotional state: ${defaultConversationConfig.patient.emotionalState.primaryMood}
- Patient openness level: ${defaultConversationConfig.patient.emotionalState.openness}
- Session progress: Turn ${this.conversationHistory.length / 2 + 1}

Your therapeutic approach should be:
- Empathy level: ${this.config.behavior.empathyLevel}
- Directness: ${this.config.behavior.directness}
- Questioning style: ${this.config.behavior.questioningStyle}
- Response length: ${this.config.behavior.responseLength}

Available techniques: ${this.config.techniques.join(', ')}

Remember to maintain professional boundaries while being genuinely caring and supportive.`;
  }

  /**
   * Build conversation messages for OpenAI
   */
  private buildConversationMessages(
    currentPatientMessage: string,
    _context: ConversationContext
  ): Array<{ role: 'user' | 'assistant'; content: string }> {
    const messages: Array<{ role: 'user' | 'assistant'; content: string }> = [];

    // Add recent conversation history (last 6 messages to stay within token limits)
    const recentHistory = this.conversationHistory.slice(-6);
    
    for (const msg of recentHistory) {
      messages.push({
        role: msg.role === 'patient' ? 'user' : 'assistant',
        content: msg.content
      });
    }

    // Add current patient message
    messages.push({
      role: 'user',
      content: currentPatientMessage
    });

    return messages;
  }

  /**
   * Analyze conversation progress
   */
  async analyzeProgress(): Promise<{
    rapportLevel: 'low' | 'medium' | 'high';
    issuesExplored: string[];
    recommendedFocus: string;
  }> {
    console.log('📊 Analyzing conversation progress...');

    try {
      const conversationSummary = this.conversationHistory
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      const analysisPrompt = `
Analyze this therapy conversation and provide:
1. Rapport level (low/medium/high)
2. Issues explored so far
3. Recommended focus for next steps

Conversation:
${conversationSummary}

Respond in JSON format:
{
  "rapportLevel": "low|medium|high",
  "issuesExplored": ["issue1", "issue2"],
  "recommendedFocus": "description"
}`;

      const response = await this.openaiService.generateResponse({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'You are analyzing a therapy conversation. Respond only with valid JSON.' },
          { role: 'user', content: analysisPrompt }
        ],
        temperature: 0.3,
        max_tokens: 200
      });

      const analysis = JSON.parse(response.message);
      console.log('📈 Progress analysis completed:', analysis);
      
      return analysis;

    } catch (error) {
      console.error('❌ Error analyzing progress:', error);
      return {
        rapportLevel: 'medium',
        issuesExplored: ['initial assessment'],
        recommendedFocus: 'Continue building rapport and exploring patient concerns'
      };
    }
  }

  /**
   * Get conversation history
   */
  getConversationHistory(): Array<{ role: string; content: string; timestamp: string }> {
    return [...this.conversationHistory];
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    console.log('🗑️ Clearing therapist conversation history');
    this.conversationHistory = [];
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<TherapistConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('⚙️ Therapist configuration updated');
  }
}
