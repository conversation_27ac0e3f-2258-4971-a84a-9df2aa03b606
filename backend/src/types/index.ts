// Backend types for MiCA Therapy Simulation

export interface AgentConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  persona?: string;
}

export interface TherapistAgent extends AgentConfig {
  type: 'therapist';
  analysisPrompts: {
    sentiment: string;
    motivation: string;
    engagement: string;
  };
}

export interface PatientAgent extends AgentConfig {
  type: 'patient';
  emotionalState: EmotionalState;
  backstory?: string;
  currentConcerns?: string[];
}

export interface EmotionalState {
  mood: 'depressed' | 'anxious' | 'neutral' | 'hopeful' | 'frustrated';
  energy: 'low' | 'medium' | 'high';
  openness: 'closed' | 'guarded' | 'open' | 'very_open';
  trust: number; // 0-100
}

export interface ConversationContext {
  id: string;
  messages: Array<{
    id: string;
    conversationId: string;
    sender: 'therapist' | 'patient';
    content: string;
    thinking: string;
    metadata: {
      sentiment: 'positive' | 'negative' | 'neutral';
      motivationLevel: 'low' | 'medium' | 'high';
      engagementLevel: 'low' | 'medium' | 'high';
      confidence: number;
      processingTime: number;
    };
    timestamp: string;
  }>;
  currentTurn: number;
  maxTurns: number;
  status: 'active' | 'completed' | 'paused';
}

export interface AgentResponse {
  message: string;
  thinking: string;
  metadata: {
    sentiment: 'positive' | 'negative' | 'neutral';
    motivationLevel: 'low' | 'medium' | 'high';
    engagementLevel: 'low' | 'medium' | 'high';
    confidence: number;
    processingTime: number;
  };
}

export interface OpenAIRequest {
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature: number;
  max_tokens: number;
}

export interface DatabaseConversation {
  id: string;
  status: string;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface DatabaseMessage {
  id: string;
  conversation_id: string;
  sender: string;
  content: string;
  metadata: any;
  created_at: string;
}

export interface DatabaseThought {
  id: string;
  conversation_id: string;
  agent_type: string;
  content: string;
  message_id?: string;
  type: string;
  created_at: string;
}

export interface WebSocketClient {
  id: string;
  socket: any;
  conversationId?: string;
  lastActivity: string;
}

export interface ConversationOrchestrator {
  conversationId: string;
  therapist: TherapistAgent;
  patient: PatientAgent;
  context: ConversationContext;
  clients: WebSocketClient[];
}
