@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-neutral-50 text-neutral-900 antialiased;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-neutral-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-neutral-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-neutral-400;
  }
}

/* Custom component styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-neutral-200 text-neutral-700 hover:bg-neutral-300 focus:ring-neutral-500;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-neutral-200;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .label {
    @apply block text-sm font-medium text-neutral-700 mb-1;
  }
  
  .message-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-2xl text-sm;
  }
  
  .message-therapist {
    @apply message-bubble bg-primary-100 text-primary-900 ml-auto;
  }
  
  .message-patient {
    @apply message-bubble bg-neutral-100 text-neutral-900 mr-auto;
  }
  
  .thinking-panel {
    @apply bg-neutral-50 border-l-4 border-neutral-300 p-4 text-sm text-neutral-600;
  }
  
  .thinking-therapist {
    @apply thinking-panel border-l-primary-400 bg-primary-50 text-primary-700;
  }
  
  .thinking-patient {
    @apply thinking-panel border-l-secondary-400 bg-secondary-50 text-secondary-700;
  }
}

/* Utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
